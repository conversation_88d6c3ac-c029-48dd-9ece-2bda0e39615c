<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-1">
				<CCardHeader>
					<div class="d-flex justify-content-start align-items-center">
						<b-dropdown size="lg" variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
							<template #button-content>
								<span class="material-icons-outlined">tune</span>
							</template>
							<b-dropdown-item 
								v-for="(tab, index) in optionTabs" 
								:key="index" 
								@click="handleClickTab(tab.value)"
							>
								{{ tab.title }} ({{ dataCounts[tab.value] }})
							</b-dropdown-item>
							<b-dropdown-divider></b-dropdown-divider>
							<b-dropdown-item>
                                <div class="d-inline-flex align-items-center">
                                    <span class="material-symbols-outlined">auto_fix_high</span>
                                    <span class="m-2">{{ $t('menu_tab.custom') }}</span>
                                </div>
                            </b-dropdown-item>
						</b-dropdown>
						<ul class="nav">
							<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
								<a 
									:class="{ active: isActiveTab(tab.value) }" 
									class="nav-link"
									@click="handleClickTab(tab.value)"
								>
									{{ tab.title }} ({{ dataCounts[tab.value] }})
								</a>
							</li>
						</ul>
						<div class="ms-auto">
                            <router-link
                                :to="{ name: 'WorkflowAdd' }"
                                class="btn btn-light d-flex align-items-center"
                            >
                                <span class="material-symbols-outlined me-1">add_circle</span>
								<span class="fw-normal">{{ $t('workflow.add') }}</span>
                            </router-link>
						</div>
					</div>
				</CCardHeader>
			</CCard>
			<CCard class="mb-4">
				<CCardHeader>
					<paginate 
						:meta="paginate.meta" 
						:links="paginate.links" 
						@page="page" 
						@per-page="perPage"
					>
					</paginate>
				</CCardHeader>
				<CCardBody>
					<workflow-table
						:dataWorkflows="dataWorkflows"
						@refresh-data="refreshCurrentPage"
					>
					</workflow-table>
				</CCardBody>
			</CCard>
		</CCol>
	</CRow>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
import WorkflowTable from '@/views/workflow/WorkflowTable.vue';
import Paginate from '@/views/paginate/Paginate.vue';
import useWorkflows from '@/composables/workflow';
import { WORKFLOWS } from '@/constants/constants';
  
export default defineComponent({
    name: "WorkflowListData",

	components: {
        Paginate,
		WorkflowTable
    },
  
    setup() {
		const route = useRoute();
		const { t }  = useI18n();

		const state = reactive({
			tabActived: '' as any,
			paginate: {
				page: 1,
            	perPage: 1,
			}
		});
		
		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
			{ value: WORKFLOWS.STATUS.ALL, title: t('option_tab_workflow.all') },
			{ value: WORKFLOWS.STATUS.ACTIVE, title: t('option_tab_workflow.active') },
			{ value: WORKFLOWS.STATUS.UNACTIVE, title: t('option_tab_workflow.unactive') },
			{ value: WORKFLOWS.STATUS.SAVE_DRAFT, title: t('option_tab_workflow.save_draft') },
		]);

		const isActiveTab = (valueTabActived: string) => {
			return route.query.tab === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
			getAllWorkflows(
				state.paginate.page, 
				state.paginate.perPage, 
				state.tabActived
			);
		};

		const { setIsLoading, paginate, dataWorkflows, dataCounts, getAllWorkflows } = useWorkflows();

		const refreshData = (): void => {
			state.tabActived = route.query.tab || WORKFLOWS.STATUS.ALL;
			getAllWorkflows(
				state.paginate.page, 
				state.paginate.perPage,
				state.tabActived
			);
        };
 
        onMounted(() => {
		 	refreshData();
		})

        const page = (page: number): void => {
            getAllWorkflows(
				page, 
				state.paginate.perPage,
				state.tabActived
			);
        };

        const perPage = (perPage: number): void => {
            state.paginate.perPage = perPage;
            getAllWorkflows(
				state.paginate.page, 
				perPage,
				state.tabActived
			);
        };

		// Refresh data while keeping current page
		const refreshCurrentPage = (): void => {
			state.tabActived = route.query.tab || WORKFLOWS.STATUS.ALL;
			// Use current page from paginate meta, fallback to state, then to 1
			const currentPage = Number(paginate.meta.currentPage) || state.paginate.page || 1;
			const currentPerPage = Number(paginate.meta.perPage) || state.paginate.perPage || 10;
			// Update state to match current pagination
			state.paginate.page = currentPage;
			state.paginate.perPage = currentPerPage;
			getAllWorkflows(
				currentPage,
				currentPerPage,
				state.tabActived
			);
		};

		return {
			state,
			optionTabs,
			setIsLoading,
			paginate,
			isActiveTab,
			handleClickTab,
			dataWorkflows,
			dataCounts,
			refreshData,
            page,
            perPage,
			refreshCurrentPage,
		}
    }
});
</script>

<style scoped>
.active {
    display: inline-block;
    padding: 10px 10px;
    font-size: 16px;
    text-align: center;
    text-decoration: none;
    color: #0b57d0 !important;
    background-color: #fff !important;
    border-bottom: 3px solid #0b57d0;
    font-weight: 600;
    line-height: 20px;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>